"""
AWS Bedrock utility functions for AI-powered data extraction.

This module provides production-ready Bedrock functionality for:
- Nova Pro model integration
- Structured data extraction from text
- Custom prompt handling
- Comprehensive logging for AWS environments
"""

import boto3
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
from botocore.exceptions import ClientError
from app.core.configuration import settings
from app.utils.text_utils import extract_json_from_backticks
from app.utils.langfuse_util import get_langfuse_util

# Configure logging
logger = logging.getLogger(__name__)


class BedrockProcessor:
    """
    AWS Bedrock processor for AI-powered data extraction using Nova Pro.
    """
    
    def __init__(self, aws_region: str, aws_access_key_id: str, aws_secret_access_key: str):
        """
        Initialize Bedrock processor.

        Args:
            aws_region: AWS region
            aws_access_key_id: AWS access key ID
            aws_secret_access_key: AWS secret access key
        """
        logger.info("🚀 Initializing BedrockProcessor...")

        # Input validation
        if not aws_region:
            raise ValueError("aws_region cannot be empty")

        try:
            self.bedrock_client = boto3.client(
                'bedrock-runtime',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id or None,
                aws_secret_access_key=aws_secret_access_key or None
            )

            self.region = aws_region

            # Validate credentials are available
            if not aws_access_key_id and not aws_secret_access_key:
                logger.warning("⚠️  AWS credentials not provided - using default credential chain")

            logger.info("✅ BedrockProcessor initialized successfully")
            logger.info(f"    AWS Region: {self.region}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize BedrockProcessor: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise

    
    async def extract_data_with_bedrock(self,
                                       system_prompt: Optional[str] = None,
                                       user_prompt: Optional[str] = None,
                                       temperature: float = 0.1,
                                       max_tokens: int = 4000) -> Dict[str, Any]:
        """
        Extract structured data using Bedrock Nova Pro model.

        Args:
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature (default: 0.1)
            max_tokens: Maximum tokens (default: 4000)

        Returns:
            dict: Extracted structured data
        """
        # Input validation
        if not system_prompt and not user_prompt:
            raise ValueError("At least one of system_prompt or user_prompt must be provided")

        logger.info(f"    Temperature: {temperature}")
        logger.info(f"    Max tokens: {max_tokens}")

        # Initialize Langfuse tracking
        langfuse_util = get_langfuse_util()
        generation = None

        if langfuse_util.is_enabled:
            generation = langfuse_util.create_generation(
                name="bedrock_extraction",
                model="amazon.nova-pro-v1:0",
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                model_parameters={
                    "temperature": temperature,
                    "max_tokens": max_tokens
                }
            )

        try:
            
            # Call Bedrock Nova Pro using converse method
            response = self.bedrock_client.converse(
                modelId="amazon.nova-pro-v1:0",
                system=[{"text": system_prompt}],
                messages=[
                    {
                        "role": "user",
                        "content": [{"text": user_prompt}]
                    }
                ],
                inferenceConfig={
                    'maxTokens': max_tokens,
                    'temperature': temperature,
                }
            )


            # Extract the content from Nova Pro response using converse format
            if 'output' in response and 'message' in response['output']:
                content = response['output']['message']['content']
                if len(content) > 0 and 'text' in content[0]:
                    extracted_content = content[0]['text']

                    # End generation with successful response
                    if generation:
                        usage_details = {
                            "input": response.get('usage', {}).get('inputTokens', 0),
                            "output": response.get('usage', {}).get('outputTokens', 0),
                            "total": response.get('usage', {}).get('totalTokens', 0)
                        } if 'usage' in response else None

                        langfuse_util.end_generation(
                            generation=generation,
                            output=extracted_content,
                            usage_details=usage_details
                        )

                    # Try to parse as JSON
                    try:
                        extracted_data = extract_json_from_backticks(extracted_content)

                        # Check if extraction was successful
                        if extracted_data.get('working_json', False):
                            logger.info("✅ Successfully extracted and parsed JSON data")
                            return extracted_data.get('data', {})
                        else:
                            logger.warning(f"⚠️  JSON extraction failed: {extracted_data.get('error', 'Unknown error')}")
                            return {"extracted_text": extracted_content, "raw_response": response, "parse_error": extracted_data.get('error')}

                    except Exception as e:
                        logger.warning(f"⚠️  Error during JSON extraction: {str(e)}")
                        return {"extracted_text": extracted_content, "raw_response": response, "parse_error": str(e)}
                else:
                    logger.warning("⚠️  Unexpected content structure in  response")
                    # End generation with error for unexpected content structure
                    if generation:
                        langfuse_util.end_generation(
                            generation=generation,
                            output="",
                            level="ERROR",
                            status_message="Unexpected content structure in response"
                        )
                    return {"raw_response": response}
            else:
                logger.warning("⚠️  Unexpected response format")
                # End generation with error for unexpected response format
                if generation:
                    langfuse_util.end_generation(
                        generation=generation,
                        output="",
                        level="ERROR",
                        status_message="Unexpected response format"
                    )
                return {"raw_response": response}

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"❌ Bedrock ClientError: {error_code} - {error_message}")
            logger.error(f"    Traceback: {traceback.format_exc()}")

            # End generation with ClientError details
            if generation:
                langfuse_util.end_generation(
                    generation=generation,
                    output="",
                    level="ERROR",
                    status_message=f"Bedrock ClientError: {error_code} - {error_message}"
                )
            raise

        except Exception as e:
            logger.error(f"❌ Unexpected error during Bedrock extraction: {str(e)}")
            logger.error(f"    Traceback: {traceback.format_exc()}")

            # End generation with general error details
            if generation:
                langfuse_util.end_generation(
                    generation=generation,
                    output="",
                    level="ERROR",
                    status_message=f"Unexpected error: {str(e)}"
                )
            raise

        finally:
            # Ensure Langfuse data is flushed
            if langfuse_util.is_enabled:
                langfuse_util.flush()
    


    async def extract_data_with_structured_text(self, 
                                              system_prompt: Optional[str] = None,
                                              user_prompt: Optional[str] = None,
                                              temperature: float = 0,
                                              max_tokens: int = 8000) -> Dict[str, Any]:
        """
        Extract structured data using both plain text and structured text with coordinates.

        Args:
            plain_text: Plain text content
            system_prompt: Optional custom system prompt
            user_prompt: Optional custom user prompt
            temperature: Model temperature
            max_tokens: Maximum tokens

        Returns:
            dict: Complete Bedrock processing results
        """

        start_time = datetime.now()

        try:
            # Use structured text system prompt if not provided
            if not system_prompt:
                system_prompt = ""

            # Create enhanced user prompt with both text types
            if not user_prompt:
                user_prompt = ""


            # Extract structured data using the enhanced prompt
            structured_data = await self.extract_data_with_bedrock(
                system_prompt, user_prompt, temperature, max_tokens
            )

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            result = {
                "bedrock_metadata": {
                    "processing_start_time": start_time.isoformat(),
                    "processing_end_time": end_time.isoformat(),
                    "total_processing_time_seconds": processing_time,
                    "aws_region": self.region,
                    "system_prompt": system_prompt,
                    "user_prompt": user_prompt,
                    "model_id": "amazon.nova-pro-v1:0",
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                },
                "structured_data": structured_data
            }

            logger.info("✅ Structured text processing completed successfully!")
            logger.info(f"    Total processing time: {processing_time:.2f} seconds")

            return result

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.error("❌ Structured text processing failed!")
            logger.error(f"    Error: {str(e)}")
            logger.error(f"    Processing time before failure: {processing_time:.2f} seconds")
            logger.error(f"    Traceback: {traceback.format_exc()}")
            raise


# Utility functions for easy access
async def extract_data_with_structured_text_bedrock(system_prompt: Optional[str] = None,
                                                  user_prompt: Optional[str] = None,
                                                  temperature: float = 0.1,
                                                  max_tokens: int = 4000,
                                                  aws_region: Optional[str] = None,
                                                  aws_access_key_id: Optional[str] = None,
                                                  aws_secret_access_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Extract structured data using both plain text and structured text with coordinates.

    Args:
        plain_text: Plain text content
        structured_text: Structured text with coordinates
        system_prompt: Optional custom system prompt
        user_prompt: Optional custom user prompt
        temperature: Model temperature
        max_tokens: Maximum tokens
        aws_region: AWS region (defaults to settings)
        aws_access_key_id: AWS access key (defaults to settings)
        aws_secret_access_key: AWS secret key (defaults to settings)

    Returns:
        dict: Bedrock processing results with enhanced coordinate-aware extraction
    """
    # Use provided credentials or fall back to settings
    region = aws_region or settings.AWS_REGION
    access_key = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
    secret_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY

    processor = BedrockProcessor(region, access_key, secret_key)
    return await processor.extract_data_with_structured_text(
        system_prompt, user_prompt, temperature, max_tokens
    )
